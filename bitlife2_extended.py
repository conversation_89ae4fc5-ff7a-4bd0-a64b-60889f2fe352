import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import random
import json
import os

class BitLife2Extended:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("BitLife 2 - Life Simulator (Extended)")
        self.root.geometry("900x700")
        self.root.configure(bg="#2C3E50")
        
        # Character stats (0-100)
        self.health = random.randint(70, 100)
        self.happiness = random.randint(60, 90)
        self.smarts = random.randint(40, 80)
        self.looks = random.randint(30, 90)
        self.money = random.randint(0, 1000)
        
        # Character info
        self.age = 0
        self.name = "<PERSON>"
        self.gender = random.choice(["Male", "Female"])
        self.job = None
        self.salary = 0
        self.education = "None"
        self.relationship_status = "Single"
        self.partner = None
        self.children = []
        self.pets = []
        
        # Game state
        self.year_events = []
        self.is_alive = True
        self.achievements = []
        
        # Extended features
        self.criminal_record = []
        self.diseases = []
        self.addictions = []
        
        self.setup_ui()
        self.generate_random_name()
        self.update_display()
    
    def generate_random_name(self):
        male_names = ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]
        female_names = ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]
        
        if self.gender == "<PERSON>":
            self.name = random.choice(male_names)
        else:
            self.name = random.choice(female_names)
    
    def setup_ui(self):
        # Main frame
        main_frame = tk.Frame(self.root, bg="#2C3E50")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Title
        title_label = tk.Label(main_frame, text="BitLife 2 Extended", font=("Arial", 24, "bold"), 
                              fg="#E74C3C", bg="#2C3E50")
        title_label.pack(pady=(0, 15))
        
        # Character info frame
        info_frame = tk.Frame(main_frame, bg="#34495E", relief=tk.RAISED, bd=2)
        info_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.info_label = tk.Label(info_frame, text="", font=("Arial", 12), 
                                  fg="white", bg="#34495E")
        self.info_label.pack(pady=8)
        
        # Stats frame
        stats_frame = tk.Frame(main_frame, bg="#2C3E50")
        stats_frame.pack(fill=tk.X, pady=(0, 15))
        
        # Create stat bars
        self.create_stat_bar(stats_frame, "Health", "#E74C3C", 0)
        self.create_stat_bar(stats_frame, "Happiness", "#F39C12", 1)
        self.create_stat_bar(stats_frame, "Smarts", "#3498DB", 2)
        self.create_stat_bar(stats_frame, "Looks", "#9B59B6", 3)
        
        # Money display
        money_frame = tk.Frame(main_frame, bg="#27AE60", relief=tk.RAISED, bd=2)
        money_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.money_label = tk.Label(money_frame, text="", font=("Arial", 14, "bold"), 
                                   fg="white", bg="#27AE60")
        self.money_label.pack(pady=8)
        
        # Age button (main action)
        self.age_button = tk.Button(main_frame, text="Age Up", font=("Arial", 16, "bold"),
                                   bg="#E74C3C", fg="white", command=self.age_up,
                                   height=2, width=15)
        self.age_button.pack(pady=10)
        
        # Activities frame with tabs
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # Basic Activities Tab
        basic_frame = tk.Frame(notebook, bg="#2C3E50")
        notebook.add(basic_frame, text="Basic")
        self.setup_basic_activities(basic_frame)
        
        # Social Tab
        social_frame = tk.Frame(notebook, bg="#2C3E50")
        notebook.add(social_frame, text="Social")
        self.setup_social_activities(social_frame)
        
        # Crime Tab
        crime_frame = tk.Frame(notebook, bg="#2C3E50")
        notebook.add(crime_frame, text="Crime")
        self.setup_crime_activities(crime_frame)
        
        # Shopping Tab
        shopping_frame = tk.Frame(notebook, bg="#2C3E50")
        notebook.add(shopping_frame, text="Shopping")
        self.setup_shopping_activities(shopping_frame)
        
        # Events display
        events_frame = tk.Frame(main_frame, bg="#34495E", relief=tk.SUNKEN, bd=2)
        events_frame.pack(fill=tk.X, pady=(10, 0))
        
        events_label = tk.Label(events_frame, text="Recent Events", font=("Arial", 12, "bold"),
                               fg="white", bg="#34495E")
        events_label.pack(pady=(5, 0))
        
        self.events_text = tk.Text(events_frame, height=4, font=("Arial", 9),
                                  bg="#2C3E50", fg="white", wrap=tk.WORD)
        self.events_text.pack(fill=tk.X, padx=10, pady=(0, 10))
    
    def setup_basic_activities(self, parent):
        # Row 1
        row1 = tk.Frame(parent, bg="#2C3E50")
        row1.pack(fill=tk.X, pady=5)
        
        self.create_activity_button(row1, "Study", self.study, "#3498DB")
        self.create_activity_button(row1, "Exercise", self.exercise, "#E74C3C")
        self.create_activity_button(row1, "Meditate", self.meditate, "#9B59B6")
        
        # Row 2
        row2 = tk.Frame(parent, bg="#2C3E50")
        row2.pack(fill=tk.X, pady=5)
        
        self.create_activity_button(row2, "Work", self.work, "#F39C12")
        self.create_activity_button(row2, "Doctor", self.visit_doctor, "#E67E22")
        self.create_activity_button(row2, "Library", self.visit_library, "#8E44AD")
        
        # Row 3
        row3 = tk.Frame(parent, bg="#2C3E50")
        row3.pack(fill=tk.X, pady=5)
        
        self.create_activity_button(row3, "Gym", self.visit_gym, "#C0392B")
        self.create_activity_button(row3, "Spa", self.visit_spa, "#16A085")
        self.create_activity_button(row3, "Casino", self.visit_casino, "#D35400")
    
    def setup_social_activities(self, parent):
        # Row 1
        row1 = tk.Frame(parent, bg="#2C3E50")
        row1.pack(fill=tk.X, pady=5)
        
        self.create_activity_button(row1, "Date", self.go_on_date, "#E91E63")
        self.create_activity_button(row1, "Party", self.attend_party, "#FF5722")
        self.create_activity_button(row1, "Make Friends", self.make_friends, "#4CAF50")
        
        # Row 2
        row2 = tk.Frame(parent, bg="#2C3E50")
        row2.pack(fill=tk.X, pady=5)
        
        self.create_activity_button(row2, "Get Married", self.get_married, "#F06292")
        self.create_activity_button(row2, "Have Baby", self.have_baby, "#FFB74D")
        self.create_activity_button(row2, "Adopt Pet", self.adopt_pet, "#81C784")
    
    def setup_crime_activities(self, parent):
        # Row 1
        row1 = tk.Frame(parent, bg="#2C3E50")
        row1.pack(fill=tk.X, pady=5)
        
        self.create_activity_button(row1, "Shoplift", self.shoplift, "#795548")
        self.create_activity_button(row1, "Pickpocket", self.pickpocket, "#607D8B")
        self.create_activity_button(row1, "Burglary", self.burglary, "#424242")
        
        # Row 2
        row2 = tk.Frame(parent, bg="#2C3E50")
        row2.pack(fill=tk.X, pady=5)
        
        self.create_activity_button(row2, "Car Theft", self.steal_car, "#37474F")
        self.create_activity_button(row2, "Bank Robbery", self.rob_bank, "#263238")
        self.create_activity_button(row2, "Prison", self.check_prison, "#1C1C1C")
    
    def setup_shopping_activities(self, parent):
        # Row 1
        row1 = tk.Frame(parent, bg="#2C3E50")
        row1.pack(fill=tk.X, pady=5)
        
        self.create_activity_button(row1, "Buy Car", self.buy_car, "#2196F3")
        self.create_activity_button(row1, "Buy House", self.buy_house, "#4CAF50")
        self.create_activity_button(row1, "Buy Clothes", self.buy_clothes, "#9C27B0")
        
        # Row 2
        row2 = tk.Frame(parent, bg="#2C3E50")
        row2.pack(fill=tk.X, pady=5)
        
        self.create_activity_button(row2, "Plastic Surgery", self.plastic_surgery, "#E91E63")
        self.create_activity_button(row2, "Vacation", self.go_on_vacation, "#FF9800")
        self.create_activity_button(row2, "Lottery", self.buy_lottery, "#FFC107")
    
    def create_stat_bar(self, parent, name, color, row):
        frame = tk.Frame(parent, bg="#2C3E50")
        frame.pack(fill=tk.X, pady=2)
        
        label = tk.Label(frame, text=f"{name}:", font=("Arial", 11), 
                        fg="white", bg="#2C3E50", width=10, anchor="w")
        label.pack(side=tk.LEFT)
        
        # Progress bar
        progress = ttk.Progressbar(frame, length=300, mode='determinate')
        progress.pack(side=tk.LEFT, padx=(10, 10))
        
        # Value label
        value_label = tk.Label(frame, text="", font=("Arial", 11), 
                              fg="white", bg="#2C3E50", width=5)
        value_label.pack(side=tk.RIGHT)
        
        # Store references
        setattr(self, f"{name.lower()}_bar", progress)
        setattr(self, f"{name.lower()}_value_label", value_label)
    
    def create_activity_button(self, parent, text, command, color):
        button = tk.Button(parent, text=text, font=("Arial", 9), bg=color, fg="white",
                          command=command, width=12, height=2)
        button.pack(side=tk.LEFT, padx=3, expand=True, fill=tk.X)

    def update_display(self):
        # Update character info
        info_text = f"{self.name} ({self.gender}) - Age: {self.age}"
        if self.job:
            info_text += f" - Job: {self.job}"
        if self.relationship_status != "Single":
            info_text += f" - {self.relationship_status}"
        if self.children:
            info_text += f" - Children: {len(self.children)}"

        self.info_label.config(text=info_text)

        # Update stat bars
        self.health_bar['value'] = max(0, min(100, self.health))
        self.happiness_bar['value'] = max(0, min(100, self.happiness))
        self.smarts_bar['value'] = max(0, min(100, self.smarts))
        self.looks_bar['value'] = max(0, min(100, self.looks))

        # Update value labels
        self.health_value_label.config(text=f"{int(self.health)}")
        self.happiness_value_label.config(text=f"{int(self.happiness)}")
        self.smarts_value_label.config(text=f"{int(self.smarts)}")
        self.looks_value_label.config(text=f"{int(self.looks)}")

        # Update money
        self.money_label.config(text=f"Money: ${self.money:,}")

        # Update events
        self.events_text.delete(1.0, tk.END)
        for event in self.year_events[-4:]:  # Show last 4 events
            self.events_text.insert(tk.END, f"• {event}\n")

    def add_event(self, event):
        self.year_events.append(f"Age {self.age}: {event}")
        self.update_display()

    def clamp_stat(self, value):
        return max(0, min(100, value))

    def age_up(self):
        if not self.is_alive:
            return

        self.age += 1

        # Natural aging effects
        if self.age > 30:
            self.health -= random.randint(1, 3)
            self.looks -= random.randint(0, 2)

        if self.age > 50:
            self.health -= random.randint(2, 5)
            self.looks -= random.randint(1, 3)

        if self.age > 70:
            self.health -= random.randint(3, 8)

        # Clamp stats
        self.health = self.clamp_stat(self.health)
        self.happiness = self.clamp_stat(self.happiness)
        self.smarts = self.clamp_stat(self.smarts)
        self.looks = self.clamp_stat(self.looks)

        # Work income
        if self.job and self.age >= 18:
            self.money += self.salary
            if self.salary > 0:
                self.add_event(f"Earned ${self.salary} from work")

        # Random events
        self.random_event()

        # Check death
        if self.health <= 0 or self.age >= 100:
            self.die()

        # Life stage changes
        self.check_life_stages()

        self.update_display()

    def random_event(self):
        events = [
            ("You found $100 on the street!", lambda: setattr(self, 'money', self.money + 100)),
            ("You got sick and lost some health", lambda: setattr(self, 'health', self.health - 15)),
            ("You had a great day and feel happier!", lambda: setattr(self, 'happiness', self.happiness + 20)),
            ("You met someone interesting", lambda: setattr(self, 'happiness', self.happiness + 8)),
            ("You had a bad day at work", lambda: setattr(self, 'happiness', self.happiness - 10)),
            ("You learned something new!", lambda: setattr(self, 'smarts', self.smarts + 5)),
            ("You got a compliment on your appearance", lambda: setattr(self, 'looks', self.looks + 3)),
            ("You spent money on something unnecessary", lambda: setattr(self, 'money', max(0, self.money - 50))),
            ("You won a small contest!", lambda: setattr(self, 'money', self.money + 200)),
            ("You helped someone and feel good about it", lambda: setattr(self, 'happiness', self.happiness + 12)),
            ("You got food poisoning", lambda: setattr(self, 'health', self.health - 8)),
            ("You discovered a new hobby", lambda: setattr(self, 'happiness', self.happiness + 10)),
        ]

        if random.random() < 0.5:  # 50% chance of random event
            event_text, event_action = random.choice(events)
            event_action()
            self.add_event(event_text)

    def check_life_stages(self):
        if self.age == 5:
            self.add_event("You started elementary school!")
        elif self.age == 12:
            self.add_event("You started middle school!")
        elif self.age == 15:
            self.add_event("You started high school!")
        elif self.age == 18:
            self.add_event("You became an adult!")
            if not self.job:
                self.look_for_job()
        elif self.age == 65:
            self.add_event("You retired!")
            self.job = None
            self.salary = 0

    def look_for_job(self):
        jobs = [
            ("Cashier", 25000),
            ("Waiter", 22000),
            ("Office Worker", 35000),
            ("Teacher", 45000),
            ("Engineer", 70000),
            ("Doctor", 120000),
            ("Lawyer", 90000),
            ("Artist", 30000),
            ("Police Officer", 55000),
            ("Firefighter", 50000),
            ("Programmer", 80000),
            ("Chef", 40000),
        ]

        # Job depends on smarts
        available_jobs = []
        for job, salary in jobs:
            if job == "Doctor" and self.smarts < 80:
                continue
            elif job == "Engineer" and self.smarts < 70:
                continue
            elif job == "Lawyer" and self.smarts < 75:
                continue
            elif job == "Teacher" and self.smarts < 60:
                continue
            elif job == "Programmer" and self.smarts < 65:
                continue
            available_jobs.append((job, salary))

        if available_jobs:
            self.job, self.salary = random.choice(available_jobs)
            self.add_event(f"You got a job as a {self.job}!")

    def die(self):
        self.is_alive = False
        self.add_event("You died. Game Over!")

        # Calculate life score
        score = (self.health + self.happiness + self.smarts + self.looks) / 4
        score += min(self.money / 1000, 50)  # Money bonus (max 50 points)
        score += len(self.children) * 5  # Children bonus
        score += self.age  # Age bonus

        messagebox.showinfo("Game Over",
                           f"{self.name} lived to age {self.age}.\n"
                           f"Life Score: {int(score)}\n"
                           f"Thanks for playing BitLife 2!")
        self.age_button.config(state=tk.DISABLED, text="Game Over")

    # Basic Activity methods
    def study(self):
        if self.age < 5:
            messagebox.showinfo("Too Young", "You're too young to study!")
            return

        smart_gain = random.randint(5, 12)
        happiness_loss = random.randint(2, 5)

        self.smarts = self.clamp_stat(self.smarts + smart_gain)
        self.happiness = self.clamp_stat(self.happiness - happiness_loss)

        self.add_event(f"You studied hard and gained {smart_gain} smarts!")
        self.update_display()

    def exercise(self):
        health_gain = random.randint(5, 10)
        happiness_gain = random.randint(2, 6)
        looks_gain = random.randint(1, 4)

        self.health = self.clamp_stat(self.health + health_gain)
        self.happiness = self.clamp_stat(self.happiness + happiness_gain)
        self.looks = self.clamp_stat(self.looks + looks_gain)

        self.add_event(f"You exercised and feel great!")
        self.update_display()

    def meditate(self):
        happiness_gain = random.randint(8, 18)
        health_gain = random.randint(2, 5)

        self.happiness = self.clamp_stat(self.happiness + happiness_gain)
        self.health = self.clamp_stat(self.health + health_gain)

        self.add_event(f"You meditated and found inner peace!")
        self.update_display()

    def work(self):
        if self.age < 16:
            messagebox.showinfo("Too Young", "You're too young to work!")
            return

        if not self.job:
            messagebox.showinfo("No Job", "You need to find a job first! Age up to 18 to get one.")
            return

        money_gain = random.randint(100, 400)
        happiness_loss = random.randint(3, 8)

        self.money += money_gain
        self.happiness = self.clamp_stat(self.happiness - happiness_loss)

        self.add_event(f"You worked extra hours and earned ${money_gain}!")
        self.update_display()

    def visit_doctor(self):
        cost = random.randint(200, 800)
        if self.money < cost:
            messagebox.showinfo("No Money", f"You need ${cost} to visit the doctor!")
            return

        self.money -= cost
        health_gain = random.randint(15, 30)
        self.health = self.clamp_stat(self.health + health_gain)

        self.add_event(f"You visited the doctor and feel much better!")
        self.update_display()

    def visit_library(self):
        smart_gain = random.randint(3, 8)
        happiness_gain = random.randint(1, 4)

        self.smarts = self.clamp_stat(self.smarts + smart_gain)
        self.happiness = self.clamp_stat(self.happiness + happiness_gain)

        self.add_event(f"You spent time at the library and learned something new!")
        self.update_display()

    def visit_gym(self):
        cost = random.randint(50, 150)
        if self.money < cost:
            messagebox.showinfo("No Money", f"You need ${cost} for a gym membership!")
            return

        self.money -= cost
        health_gain = random.randint(8, 15)
        looks_gain = random.randint(3, 8)
        happiness_gain = random.randint(2, 6)

        self.health = self.clamp_stat(self.health + health_gain)
        self.looks = self.clamp_stat(self.looks + looks_gain)
        self.happiness = self.clamp_stat(self.happiness + happiness_gain)

        self.add_event(f"You had a great workout at the gym!")
        self.update_display()

    def visit_spa(self):
        cost = random.randint(200, 500)
        if self.money < cost:
            messagebox.showinfo("No Money", f"You need ${cost} for a spa day!")
            return

        self.money -= cost
        happiness_gain = random.randint(15, 25)
        looks_gain = random.randint(5, 12)
        health_gain = random.randint(3, 8)

        self.happiness = self.clamp_stat(self.happiness + happiness_gain)
        self.looks = self.clamp_stat(self.looks + looks_gain)
        self.health = self.clamp_stat(self.health + health_gain)

        self.add_event(f"You had a relaxing spa day!")
        self.update_display()

    def visit_casino(self):
        if self.age < 18:
            messagebox.showinfo("Too Young", "You must be 18 to gamble!")
            return

        bet = random.randint(50, 500)
        if self.money < bet:
            messagebox.showinfo("No Money", f"You need at least ${bet} to gamble!")
            return

        self.money -= bet

        if random.random() < 0.3:  # 30% chance to win
            winnings = bet * random.randint(2, 5)
            self.money += winnings
            self.happiness = self.clamp_stat(self.happiness + 20)
            self.add_event(f"You won ${winnings} at the casino!")
        else:
            self.happiness = self.clamp_stat(self.happiness - 10)
            self.add_event(f"You lost ${bet} at the casino!")

        self.update_display()

    # Social Activity methods
    def go_on_date(self):
        if self.age < 16:
            messagebox.showinfo("Too Young", "You're too young to date!")
            return

        cost = random.randint(50, 200)
        if self.money < cost:
            messagebox.showinfo("No Money", f"You need ${cost} for a date!")
            return

        self.money -= cost
        happiness_gain = random.randint(10, 25)
        self.happiness = self.clamp_stat(self.happiness + happiness_gain)

        if random.random() < 0.3 and self.relationship_status == "Single":
            partner_names = ["Alex", "Jordan", "Taylor", "Casey", "Morgan", "Riley", "Avery", "Quinn"]
            self.partner = random.choice(partner_names)
            self.relationship_status = "Dating"
            self.add_event(f"You went on a great date with {self.partner}!")
        else:
            self.add_event(f"You had a nice date!")

        self.update_display()

    def attend_party(self):
        if self.age < 14:
            messagebox.showinfo("Too Young", "You're too young for parties!")
            return

        happiness_gain = random.randint(8, 20)
        self.happiness = self.clamp_stat(self.happiness + happiness_gain)

        # Random party events
        if random.random() < 0.2:
            self.health = self.clamp_stat(self.health - 5)
            self.add_event("You partied too hard and feel sick!")
        elif random.random() < 0.3:
            self.looks = self.clamp_stat(self.looks + 3)
            self.add_event("You met some cool people at the party!")
        else:
            self.add_event("You had a great time at the party!")

        self.update_display()

    def make_friends(self):
        happiness_gain = random.randint(5, 15)
        self.happiness = self.clamp_stat(self.happiness + happiness_gain)

        self.add_event("You made some new friends!")
        self.update_display()

    def get_married(self):
        if self.age < 18:
            messagebox.showinfo("Too Young", "You must be 18 to get married!")
            return

        if self.relationship_status != "Dating":
            messagebox.showinfo("No Partner", "You need to be dating someone first!")
            return

        cost = random.randint(5000, 20000)
        if self.money < cost:
            messagebox.showinfo("No Money", f"You need ${cost} for a wedding!")
            return

        self.money -= cost
        self.relationship_status = "Married"
        happiness_gain = random.randint(20, 40)
        self.happiness = self.clamp_stat(self.happiness + happiness_gain)

        self.add_event(f"You married {self.partner}! Congratulations!")
        self.update_display()

    def have_baby(self):
        if self.age < 16:
            messagebox.showinfo("Too Young", "You're too young to have a baby!")
            return

        if self.relationship_status not in ["Dating", "Married"]:
            messagebox.showinfo("No Partner", "You need a partner to have a baby!")
            return

        baby_names = ["Emma", "Liam", "Olivia", "Noah", "Ava", "Ethan", "Sophia", "Mason", "Isabella", "William"]
        baby_name = random.choice(baby_names)
        self.children.append(baby_name)

        happiness_gain = random.randint(15, 30)
        self.happiness = self.clamp_stat(self.happiness + happiness_gain)
        self.money = max(0, self.money - 2000)  # Baby expenses

        self.add_event(f"You had a baby! Welcome {baby_name}!")
        self.update_display()

    def adopt_pet(self):
        if self.age < 10:
            messagebox.showinfo("Too Young", "You're too young to adopt a pet!")
            return

        cost = random.randint(100, 500)
        if self.money < cost:
            messagebox.showinfo("No Money", f"You need ${cost} to adopt a pet!")
            return

        self.money -= cost
        pet_types = ["Dog", "Cat", "Bird", "Fish", "Hamster", "Rabbit"]
        pet_names = ["Buddy", "Luna", "Max", "Bella", "Charlie", "Lucy", "Cooper", "Daisy"]

        pet_type = random.choice(pet_types)
        pet_name = random.choice(pet_names)
        self.pets.append(f"{pet_name} the {pet_type}")

        happiness_gain = random.randint(10, 20)
        self.happiness = self.clamp_stat(self.happiness + happiness_gain)

        self.add_event(f"You adopted {pet_name} the {pet_type}!")
        self.update_display()

    # Crime Activity methods
    def shoplift(self):
        if self.age < 12:
            messagebox.showinfo("Too Young", "You're too young for crime!")
            return

        if random.random() < 0.6:  # 60% success rate
            stolen_value = random.randint(20, 100)
            self.money += stolen_value
            self.happiness = self.clamp_stat(self.happiness + 5)
            self.add_event(f"You successfully shoplifted items worth ${stolen_value}!")
        else:
            self.criminal_record.append("Shoplifting")
            self.happiness = self.clamp_stat(self.happiness - 15)
            self.add_event("You got caught shoplifting and were arrested!")

        self.update_display()

    def pickpocket(self):
        if self.age < 14:
            messagebox.showinfo("Too Young", "You're too young for crime!")
            return

        if random.random() < 0.4:  # 40% success rate
            stolen_money = random.randint(50, 300)
            self.money += stolen_money
            self.add_event(f"You pickpocketed ${stolen_money}!")
        else:
            self.criminal_record.append("Pickpocketing")
            self.happiness = self.clamp_stat(self.happiness - 20)
            self.add_event("You got caught pickpocketing and were arrested!")

        self.update_display()

    def burglary(self):
        if self.age < 16:
            messagebox.showinfo("Too Young", "You're too young for serious crime!")
            return

        if random.random() < 0.3:  # 30% success rate
            stolen_value = random.randint(500, 2000)
            self.money += stolen_value
            self.add_event(f"You successfully burglarized a house and stole ${stolen_value}!")
        else:
            self.criminal_record.append("Burglary")
            self.happiness = self.clamp_stat(self.happiness - 30)
            self.add_event("You got caught during a burglary and were arrested!")

        self.update_display()

    def steal_car(self):
        if self.age < 18:
            messagebox.showinfo("Too Young", "You're too young for grand theft auto!")
            return

        if random.random() < 0.2:  # 20% success rate
            car_value = random.randint(2000, 10000)
            self.money += car_value
            self.add_event(f"You successfully stole a car worth ${car_value}!")
        else:
            self.criminal_record.append("Grand Theft Auto")
            self.happiness = self.clamp_stat(self.happiness - 40)
            self.add_event("You got caught stealing a car and were arrested!")

        self.update_display()

    def rob_bank(self):
        if self.age < 21:
            messagebox.showinfo("Too Young", "You're too young for bank robbery!")
            return

        if random.random() < 0.1:  # 10% success rate
            stolen_money = random.randint(10000, 50000)
            self.money += stolen_money
            self.add_event(f"You successfully robbed a bank and got ${stolen_money}!")
        else:
            self.criminal_record.append("Bank Robbery")
            self.happiness = self.clamp_stat(self.happiness - 50)
            self.add_event("You got caught robbing a bank and were arrested!")

        self.update_display()

    def check_prison(self):
        if not self.criminal_record:
            messagebox.showinfo("Clean Record", "You have a clean criminal record!")
        else:
            crimes = ", ".join(self.criminal_record)
            messagebox.showinfo("Criminal Record", f"Your crimes: {crimes}")

    # Shopping Activity methods
    def buy_car(self):
        if self.age < 16:
            messagebox.showinfo("Too Young", "You're too young to buy a car!")
            return

        cost = random.randint(5000, 50000)
        if self.money < cost:
            messagebox.showinfo("No Money", f"You need ${cost} to buy a car!")
            return

        self.money -= cost
        happiness_gain = random.randint(10, 25)
        looks_gain = random.randint(2, 8)

        self.happiness = self.clamp_stat(self.happiness + happiness_gain)
        self.looks = self.clamp_stat(self.looks + looks_gain)

        car_types = ["Honda Civic", "Toyota Camry", "BMW 3 Series", "Mercedes C-Class", "Audi A4", "Tesla Model 3"]
        car = random.choice(car_types)
        self.add_event(f"You bought a {car} for ${cost}!")
        self.update_display()

    def buy_house(self):
        if self.age < 18:
            messagebox.showinfo("Too Young", "You're too young to buy a house!")
            return

        cost = random.randint(100000, 500000)
        if self.money < cost:
            messagebox.showinfo("No Money", f"You need ${cost} to buy a house!")
            return

        self.money -= cost
        happiness_gain = random.randint(20, 40)
        self.happiness = self.clamp_stat(self.happiness + happiness_gain)

        house_types = ["Apartment", "Townhouse", "Single Family Home", "Condo", "Mansion"]
        house = random.choice(house_types)
        self.add_event(f"You bought a {house} for ${cost}!")
        self.update_display()

    def buy_clothes(self):
        cost = random.randint(50, 500)
        if self.money < cost:
            messagebox.showinfo("No Money", f"You need ${cost} for new clothes!")
            return

        self.money -= cost
        looks_gain = random.randint(3, 10)
        happiness_gain = random.randint(2, 8)

        self.looks = self.clamp_stat(self.looks + looks_gain)
        self.happiness = self.clamp_stat(self.happiness + happiness_gain)

        self.add_event(f"You bought new clothes and look great!")
        self.update_display()

    def plastic_surgery(self):
        if self.age < 18:
            messagebox.showinfo("Too Young", "You must be 18 for plastic surgery!")
            return

        cost = random.randint(2000, 10000)
        if self.money < cost:
            messagebox.showinfo("No Money", f"You need ${cost} for plastic surgery!")
            return

        self.money -= cost

        if random.random() < 0.8:  # 80% success rate
            looks_gain = random.randint(10, 25)
            self.looks = self.clamp_stat(self.looks + looks_gain)
            self.add_event(f"Your plastic surgery was successful! You look amazing!")
        else:
            looks_loss = random.randint(5, 15)
            self.looks = self.clamp_stat(self.looks - looks_loss)
            self.add_event(f"Your plastic surgery went wrong! You look worse than before!")

        self.update_display()

    def go_on_vacation(self):
        cost = random.randint(1000, 5000)
        if self.money < cost:
            messagebox.showinfo("No Money", f"You need ${cost} for a vacation!")
            return

        self.money -= cost
        happiness_gain = random.randint(20, 40)
        health_gain = random.randint(5, 15)

        self.happiness = self.clamp_stat(self.happiness + happiness_gain)
        self.health = self.clamp_stat(self.health + health_gain)

        destinations = ["Paris", "Tokyo", "New York", "London", "Rome", "Sydney", "Dubai", "Hawaii"]
        destination = random.choice(destinations)
        self.add_event(f"You had an amazing vacation in {destination}!")
        self.update_display()

    def buy_lottery(self):
        if self.age < 18:
            messagebox.showinfo("Too Young", "You must be 18 to buy lottery tickets!")
            return

        cost = random.randint(5, 50)
        if self.money < cost:
            messagebox.showinfo("No Money", f"You need ${cost} for lottery tickets!")
            return

        self.money -= cost

        if random.random() < 0.01:  # 1% chance to win big
            winnings = random.randint(10000, 1000000)
            self.money += winnings
            self.happiness = self.clamp_stat(self.happiness + 50)
            self.add_event(f"YOU WON THE LOTTERY! You won ${winnings}!")
        elif random.random() < 0.1:  # 10% chance to win small
            winnings = random.randint(100, 1000)
            self.money += winnings
            self.happiness = self.clamp_stat(self.happiness + 10)
            self.add_event(f"You won ${winnings} from the lottery!")
        else:
            self.happiness = self.clamp_stat(self.happiness - 2)
            self.add_event(f"You didn't win anything from the lottery.")

        self.update_display()

    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    game = BitLife2Extended()
    game.run()
