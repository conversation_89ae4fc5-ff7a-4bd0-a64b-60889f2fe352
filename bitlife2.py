import tkinter as tk
from tkinter import ttk, messagebox
import random
import json
import os

class BitLife2:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("BitLife 2 - Life Simulator")
        self.root.geometry("800x600")
        self.root.configure(bg="#2C3E50")
        
        # Character stats (0-100)
        self.health = random.randint(70, 100)
        self.happiness = random.randint(60, 90)
        self.smarts = random.randint(40, 80)
        self.looks = random.randint(30, 90)
        self.money = random.randint(0, 1000)
        
        # Character info
        self.age = 0
        self.name = "Baby"
        self.gender = random.choice(["Male", "Female"])
        self.job = None
        self.salary = 0
        self.education = "None"
        self.relationship_status = "Single"
        self.partner = None
        self.children = []
        
        # Game state
        self.year_events = []
        self.is_alive = True
        
        self.setup_ui()
        self.generate_random_name()
        self.update_display()
    
    def generate_random_name(self):
        male_names = ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]
        female_names = ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]
        
        if self.gender == "Male":
            self.name = random.choice(male_names)
        else:
            self.name = random.choice(female_names)
    
    def setup_ui(self):
        # Main frame
        main_frame = tk.Frame(self.root, bg="#2C3E50")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Title
        title_label = tk.Label(main_frame, text="BitLife 2", font=("Arial", 24, "bold"), 
                              fg="#E74C3C", bg="#2C3E50")
        title_label.pack(pady=(0, 20))
        
        # Character info frame
        info_frame = tk.Frame(main_frame, bg="#34495E", relief=tk.RAISED, bd=2)
        info_frame.pack(fill=tk.X, pady=(0, 20))
        
        self.info_label = tk.Label(info_frame, text="", font=("Arial", 14), 
                                  fg="white", bg="#34495E")
        self.info_label.pack(pady=10)
        
        # Stats frame
        stats_frame = tk.Frame(main_frame, bg="#2C3E50")
        stats_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Create stat bars
        self.create_stat_bar(stats_frame, "Health", "#E74C3C", 0)
        self.create_stat_bar(stats_frame, "Happiness", "#F39C12", 1)
        self.create_stat_bar(stats_frame, "Smarts", "#3498DB", 2)
        self.create_stat_bar(stats_frame, "Looks", "#9B59B6", 3)
        
        # Money display
        money_frame = tk.Frame(main_frame, bg="#27AE60", relief=tk.RAISED, bd=2)
        money_frame.pack(fill=tk.X, pady=(0, 20))
        
        self.money_label = tk.Label(money_frame, text="", font=("Arial", 16, "bold"), 
                                   fg="white", bg="#27AE60")
        self.money_label.pack(pady=10)
        
        # Activities frame
        activities_frame = tk.Frame(main_frame, bg="#2C3E50")
        activities_frame.pack(fill=tk.BOTH, expand=True)
        
        # Age button (main action)
        self.age_button = tk.Button(activities_frame, text="Age Up", font=("Arial", 16, "bold"),
                                   bg="#E74C3C", fg="white", command=self.age_up,
                                   height=2, width=15)
        self.age_button.pack(pady=10)
        
        # Activity buttons
        button_frame = tk.Frame(activities_frame, bg="#2C3E50")
        button_frame.pack(fill=tk.X, pady=10)
        
        # Row 1
        row1 = tk.Frame(button_frame, bg="#2C3E50")
        row1.pack(fill=tk.X, pady=5)
        
        self.create_activity_button(row1, "Study", self.study, "#3498DB")
        self.create_activity_button(row1, "Exercise", self.exercise, "#E74C3C")
        self.create_activity_button(row1, "Meditate", self.meditate, "#9B59B6")
        
        # Row 2
        row2 = tk.Frame(button_frame, bg="#2C3E50")
        row2.pack(fill=tk.X, pady=5)
        
        self.create_activity_button(row2, "Work", self.work, "#F39C12")
        self.create_activity_button(row2, "Socialize", self.socialize, "#1ABC9C")
        self.create_activity_button(row2, "Doctor", self.visit_doctor, "#E67E22")
        
        # Events display
        events_frame = tk.Frame(main_frame, bg="#34495E", relief=tk.SUNKEN, bd=2)
        events_frame.pack(fill=tk.X, pady=(20, 0))
        
        events_label = tk.Label(events_frame, text="Recent Events", font=("Arial", 12, "bold"),
                               fg="white", bg="#34495E")
        events_label.pack(pady=(5, 0))
        
        self.events_text = tk.Text(events_frame, height=4, font=("Arial", 10),
                                  bg="#2C3E50", fg="white", wrap=tk.WORD)
        self.events_text.pack(fill=tk.X, padx=10, pady=(0, 10))
    
    def create_stat_bar(self, parent, name, color, row):
        frame = tk.Frame(parent, bg="#2C3E50")
        frame.pack(fill=tk.X, pady=2)
        
        label = tk.Label(frame, text=f"{name}:", font=("Arial", 12), 
                        fg="white", bg="#2C3E50", width=10, anchor="w")
        label.pack(side=tk.LEFT)
        
        # Progress bar
        progress = ttk.Progressbar(frame, length=300, mode='determinate')
        progress.pack(side=tk.LEFT, padx=(10, 10))
        
        # Value label
        value_label = tk.Label(frame, text="", font=("Arial", 12), 
                              fg="white", bg="#2C3E50", width=5)
        value_label.pack(side=tk.RIGHT)
        
        # Store references
        setattr(self, f"{name.lower()}_bar", progress)
        setattr(self, f"{name.lower()}_value_label", value_label)
    
    def create_activity_button(self, parent, text, command, color):
        button = tk.Button(parent, text=text, font=("Arial", 10), bg=color, fg="white",
                          command=command, width=12, height=2)
        button.pack(side=tk.LEFT, padx=5, expand=True, fill=tk.X)
    
    def update_display(self):
        # Update character info
        info_text = f"{self.name} ({self.gender}) - Age: {self.age}"
        if self.job:
            info_text += f" - Job: {self.job}"
        if self.relationship_status != "Single":
            info_text += f" - {self.relationship_status}"
        
        self.info_label.config(text=info_text)
        
        # Update stat bars
        self.health_bar['value'] = max(0, min(100, self.health))
        self.happiness_bar['value'] = max(0, min(100, self.happiness))
        self.smarts_bar['value'] = max(0, min(100, self.smarts))
        self.looks_bar['value'] = max(0, min(100, self.looks))
        
        # Update value labels
        self.health_value_label.config(text=f"{int(self.health)}")
        self.happiness_value_label.config(text=f"{int(self.happiness)}")
        self.smarts_value_label.config(text=f"{int(self.smarts)}")
        self.looks_value_label.config(text=f"{int(self.looks)}")
        
        # Update money
        self.money_label.config(text=f"Money: ${self.money:,}")
        
        # Update events
        self.events_text.delete(1.0, tk.END)
        for event in self.year_events[-3:]:  # Show last 3 events
            self.events_text.insert(tk.END, f"• {event}\n")
    
    def add_event(self, event):
        self.year_events.append(f"Age {self.age}: {event}")
        self.update_display()
    
    def clamp_stat(self, value):
        return max(0, min(100, value))

    def age_up(self):
        if not self.is_alive:
            return

        self.age += 1

        # Natural aging effects
        if self.age > 30:
            self.health -= random.randint(1, 3)
            self.looks -= random.randint(0, 2)

        if self.age > 50:
            self.health -= random.randint(2, 5)
            self.looks -= random.randint(1, 3)

        if self.age > 70:
            self.health -= random.randint(3, 8)

        # Clamp stats
        self.health = self.clamp_stat(self.health)
        self.happiness = self.clamp_stat(self.happiness)
        self.smarts = self.clamp_stat(self.smarts)
        self.looks = self.clamp_stat(self.looks)

        # Work income
        if self.job and self.age >= 18:
            self.money += self.salary
            if self.salary > 0:
                self.add_event(f"Earned ${self.salary} from work")

        # Random events
        self.random_event()

        # Check death
        if self.health <= 0 or self.age >= 100:
            self.die()

        # Life stage changes
        self.check_life_stages()

        self.update_display()

    def random_event(self):
        events = [
            ("You found $50 on the street!", lambda: setattr(self, 'money', self.money + 50)),
            ("You got sick and lost some health", lambda: setattr(self, 'health', self.health - 10)),
            ("You had a great day and feel happier!", lambda: setattr(self, 'happiness', self.happiness + 15)),
            ("You met someone interesting", lambda: setattr(self, 'happiness', self.happiness + 5)),
            ("You had a bad day at work", lambda: setattr(self, 'happiness', self.happiness - 8)),
            ("You learned something new!", lambda: setattr(self, 'smarts', self.smarts + 3)),
            ("You got a compliment on your appearance", lambda: setattr(self, 'looks', self.looks + 2)),
            ("You spent money on something unnecessary", lambda: setattr(self, 'money', max(0, self.money - 30))),
        ]

        if random.random() < 0.4:  # 40% chance of random event
            event_text, event_action = random.choice(events)
            event_action()
            self.add_event(event_text)

    def check_life_stages(self):
        if self.age == 5:
            self.add_event("You started elementary school!")
        elif self.age == 12:
            self.add_event("You started middle school!")
        elif self.age == 15:
            self.add_event("You started high school!")
        elif self.age == 18:
            self.add_event("You became an adult!")
            if not self.job:
                self.look_for_job()
        elif self.age == 65:
            self.add_event("You retired!")
            self.job = None
            self.salary = 0

    def look_for_job(self):
        jobs = [
            ("Cashier", 25000),
            ("Waiter", 22000),
            ("Office Worker", 35000),
            ("Teacher", 45000),
            ("Engineer", 70000),
            ("Doctor", 120000),
            ("Lawyer", 90000),
            ("Artist", 30000),
        ]

        # Job depends on smarts
        available_jobs = []
        for job, salary in jobs:
            if job == "Doctor" and self.smarts < 80:
                continue
            elif job == "Engineer" and self.smarts < 70:
                continue
            elif job == "Lawyer" and self.smarts < 75:
                continue
            elif job == "Teacher" and self.smarts < 60:
                continue
            available_jobs.append((job, salary))

        if available_jobs:
            self.job, self.salary = random.choice(available_jobs)
            self.add_event(f"You got a job as a {self.job}!")

    def die(self):
        self.is_alive = False
        self.add_event("You died. Game Over!")
        messagebox.showinfo("Game Over", f"{self.name} lived to age {self.age}. Thanks for playing!")
        self.age_button.config(state=tk.DISABLED, text="Game Over")

    # Activity methods
    def study(self):
        if self.age < 5:
            messagebox.showinfo("Too Young", "You're too young to study!")
            return

        smart_gain = random.randint(3, 8)
        happiness_loss = random.randint(1, 3)

        self.smarts = self.clamp_stat(self.smarts + smart_gain)
        self.happiness = self.clamp_stat(self.happiness - happiness_loss)

        self.add_event(f"You studied hard and gained {smart_gain} smarts!")
        self.update_display()

    def exercise(self):
        health_gain = random.randint(3, 7)
        happiness_gain = random.randint(1, 4)
        looks_gain = random.randint(1, 3)

        self.health = self.clamp_stat(self.health + health_gain)
        self.happiness = self.clamp_stat(self.happiness + happiness_gain)
        self.looks = self.clamp_stat(self.looks + looks_gain)

        self.add_event(f"You exercised and feel great!")
        self.update_display()

    def meditate(self):
        happiness_gain = random.randint(5, 12)
        health_gain = random.randint(1, 3)

        self.happiness = self.clamp_stat(self.happiness + happiness_gain)
        self.health = self.clamp_stat(self.health + health_gain)

        self.add_event(f"You meditated and found inner peace!")
        self.update_display()

    def work(self):
        if self.age < 16:
            messagebox.showinfo("Too Young", "You're too young to work!")
            return

        if not self.job:
            messagebox.showinfo("No Job", "You need to find a job first! Age up to 18 to get one.")
            return

        money_gain = random.randint(50, 200)
        happiness_loss = random.randint(2, 5)

        self.money += money_gain
        self.happiness = self.clamp_stat(self.happiness - happiness_loss)

        self.add_event(f"You worked extra hours and earned ${money_gain}!")
        self.update_display()

    def socialize(self):
        happiness_gain = random.randint(5, 15)
        looks_gain = random.randint(0, 2)
        money_loss = random.randint(10, 50)

        self.happiness = self.clamp_stat(self.happiness + happiness_gain)
        self.looks = self.clamp_stat(self.looks + looks_gain)
        self.money = max(0, self.money - money_loss)

        self.add_event(f"You had fun with friends!")
        self.update_display()

    def visit_doctor(self):
        cost = random.randint(100, 500)
        if self.money < cost:
            messagebox.showinfo("No Money", f"You need ${cost} to visit the doctor!")
            return

        self.money -= cost
        health_gain = random.randint(10, 25)
        self.health = self.clamp_stat(self.health + health_gain)

        self.add_event(f"You visited the doctor and feel much better!")
        self.update_display()

    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    game = BitLife2()
    game.run()
