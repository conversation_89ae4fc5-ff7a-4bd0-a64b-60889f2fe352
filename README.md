# BitLife 2 - Life Simulator

Ein vollständiges Life-Simulation-<PERSON>piel inspiriert von BitLife, erste<PERSON><PERSON> in Python mit tkinter.

## 🎮 Spielversionen

### 1. `bitlife2.py` - Basis Version
- Grundlegende Spielmechaniken
- 4 Hauptstats: Health, Happiness, Smarts, Looks
- Einfache Aktivitäten und Altern-System
- Perfekt für Einsteiger

### 2. `bitlife2_extended.py` - Erweiterte Version
- Alle Features der Basis-Version
- Erweiterte Aktivitäten in Tabs organisiert
- Kriminalitäts-System
- Shopping und Lifestyle-Optionen
- Mehr zufällige Events

### 3. `bitlife2_final.py` - Finale Edition ⭐
- Alle Features der erweiterten Version
- **Speicher/Lade-System** - Speichere deinen Fortschritt!
- Verbessertes UI mit Emojis und besserer Optik
- Achievements-System
- Detaillierte Statistiken
- Menüleiste mit Optionen
- Scrollbare Event-Anzeige

## 🚀 Wie zu spielen

### Installation
```bash
# Keine Installation nötig! Nur Python 3.x erforderlich
python bitlife2_final.py
```

### Spielmechaniken

#### 📊 Hauptstats (0-100)
- **Health** 🏥: Deine körperliche Gesundheit
- **Happiness** 😊: Deine mentale Zufriedenheit  
- **Smarts** 🧠: Deine Intelligenz
- **Looks** ✨: Dein Aussehen

#### 💰 Geld-System
- Verdiene Geld durch Arbeit
- Gib Geld für Aktivitäten aus
- Kaufe Autos, Häuser und mehr

#### 🎂 Altern
- Klicke "Age Up" um ein Jahr älter zu werden
- Jedes Jahr passieren zufällige Events
- Verschiedene Lebensphasen (Schule, Arbeit, Rente)

### 🎯 Aktivitäten

#### 📚 Basic Tab
- **Study**: Erhöhe Smarts, reduziere Happiness
- **Exercise**: Erhöhe Health, Happiness und Looks
- **Meditate**: Erhöhe Happiness und Health
- **Work**: Verdiene extra Geld (kostet Happiness)
- **Doctor**: Bezahle für bessere Health
- **Library**: Kostenlos Smarts erhöhen
- **Gym**: Bezahle für bessere Health und Looks
- **Spa**: Luxus für Happiness und Looks
- **Casino**: Glücksspiel (18+ erforderlich)

#### 👥 Social Tab
- **Date**: Finde einen Partner
- **Party**: Spaß haben (kann riskant sein)
- **Make Friends**: Erhöhe Happiness
- **Get Married**: Heirate deinen Partner (kostet viel!)
- **Have Baby**: Gründe eine Familie
- **Adopt Pet**: Hole dir einen tierischen Freund

#### 🔫 Crime Tab ⚠️
- **Shoplift**: Kleine Diebstähle (60% Erfolgsrate)
- **Pickpocket**: Taschendiebstahl (40% Erfolgsrate)
- **Burglary**: Einbruch (30% Erfolgsrate)
- **Car Theft**: Autodiebstahl (20% Erfolgsrate)
- **Bank Robbery**: Bankraub (10% Erfolgsrate)
- **Prison**: Überprüfe deine Straftaten

#### 🛍️ Shopping Tab
- **Buy Car**: Kaufe Fahrzeuge für Status
- **Buy House**: Investiere in Immobilien
- **Buy Clothes**: Verbessere dein Aussehen
- **Plastic Surgery**: Riskante Schönheits-OP
- **Vacation**: Entspanne dich im Urlaub
- **Lottery**: Versuche dein Glück

## 🏆 Achievements
- **Adult**: Werde 18 Jahre alt
- **Rich**: Erreiche $100,000
- **Big Family**: Habe 5+ Kinder
- **Senior**: Erreiche 80 Jahre
- **Perfect Life**: Alle Stats auf 90+

## 💾 Speicher-System (Nur Finale Version)
- **File → Save Game**: Speichere deinen Fortschritt
- **File → Load Game**: Lade ein gespeichertes Spiel
- **File → New Life**: Starte ein neues Leben

## 🎯 Tipps für ein erfolgreiches Leben

### Frühe Jahre (0-18)
- Studiere viel für hohe Smarts
- Treibe Sport für Health und Looks
- Spare Geld für später

### Erwachsenenalter (18-65)
- Finde einen gut bezahlten Job
- Heirate und gründe eine Familie
- Investiere in Häuser und Autos
- Besuche regelmäßig den Arzt

### Späte Jahre (65+)
- Genieße deine Rente
- Verbringe Zeit mit Familie
- Halte deine Health hoch

### ⚠️ Vermeide
- Zu viel Kriminalität (führt zu Verhaftungen)
- Vernachlässigung der Health (kann zum Tod führen)
- Zu viel Geld ausgeben ohne Einkommen

## 🎮 Spielziel
Lebe das beste virtuelle Leben! Erreiche hohe Stats, sammle Achievements und siehe wie alt du werden kannst. Jedes Leben ist einzigartig durch zufällige Events und deine Entscheidungen.

## 🔧 Technische Details
- **Sprache**: Python 3.x
- **GUI**: tkinter (standardmäßig in Python enthalten)
- **Speicherformat**: JSON
- **Plattform**: Windows, Mac, Linux

## 🎉 Viel Spaß!
Genieße dein virtuelles Leben in BitLife 2! Experimentiere mit verschiedenen Lebenswegen und siehe welche Geschichten sich entwickeln.

---
*Erstellt mit Python und tkinter - Ein BitLife-inspiriertes Lebenssimulationsspiel*
